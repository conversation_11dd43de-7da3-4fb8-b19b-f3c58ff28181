import { useState, useEffect, useRef } from "react";
import { FFmpeg } from "@ffmpeg/ffmpeg";
import { trackEvent } from "@/lib/analytics";
import { detectSIMDSupport, getBrowserInfo } from "@/lib/browserUtils";

// FFmpeg版本号，用于URL版本控制和缓存管理
const FFMPEG_VERSION = "01210"; // 对应@ffmpeg/core 0.12.10

// 全局单例FFmpeg实例
let globalFFmpegInstance = null;
let isFFmpegLoading = false;
let loadingPromise = null;
let isGlobalFFmpegReady = false; // 添加全局就绪标志

export const useFFmpegProcessor = () => {
  const [ffmpeg, setFFmpeg] = useState(null);
  const ffmpegInstanceRef = useRef(null);
  const [processing, setProcessing] = useState(false);
  const [processingType, setProcessingType] = useState(null);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState(null);
  const [isFFmpegReady, setIsFFmpegReady] = useState(false);

  const abortControllerRef = useRef(null);
  const isInterruptedRef = useRef(false);
  const isProcessingFileRef = useRef(false); // 添加文件处理标志

  const initializeFFmpeg = async () => {
    // 检查浏览器是否支持 SIMD
    const simdSupported = await detectSIMDSupport();

    // 如果浏览器不支持 SIMD，直接返回 false，不尝试加载 FFmpeg
    if (!simdSupported) {
      console.log(
        "Browser does not support WebAssembly SIMD. Skipping FFmpeg initialization."
      );
      trackEvent("ffmpeg_simd_not_supported", {
        userAgent: navigator.userAgent,
      });
      setIsFFmpegReady(false);
      return false;
    }

    // 如果已经有全局实例并且已就绪，直接使用
    if (globalFFmpegInstance && isGlobalFFmpegReady) {
      console.log("Using existing ready FFmpeg instance");
      ffmpegInstanceRef.current = globalFFmpegInstance;
      setFFmpeg(globalFFmpegInstance);
      setIsFFmpegReady(true);
      return true;
    }

    // 如果正在加载中，等待加载完成
    if (isFFmpegLoading && loadingPromise) {
      console.log("FFmpeg is already loading, waiting...");
      try {
        const ffmpegInstance = await loadingPromise;
        // 关键修复：等待完成后，使用全局实例而不是返回的实例
        // 因为返回的实例可能只对第一个调用者有效
        if (globalFFmpegInstance && isGlobalFFmpegReady) {
          console.log(
            "FFmpeg loading completed, setting local reference to global instance"
          );
          ffmpegInstanceRef.current = globalFFmpegInstance; // 使用全局实例
          setFFmpeg(globalFFmpegInstance);
          setIsFFmpegReady(true);
          return true;
        } else {
          console.error(
            "FFmpeg loading completed but global instance is not ready",
            {
              hasReturnedInstance: !!ffmpegInstance,
              hasGlobalInstance: !!globalFFmpegInstance,
              isGlobalReady: isGlobalFFmpegReady,
            }
          );
          setError("Failed to initialize media processor");
          setIsFFmpegReady(false);
          return false;
        }
      } catch (err) {
        console.error("Error while waiting for FFmpeg to load:", err);
        trackEvent("ffmpeg_wait_loading_failed", {
          error: String(err),
        });
        setError("Failed to initialize media processor");
        setIsFFmpegReady(false);
        return false;
      }
    }

    // 开始新的加载过程
    isFFmpegLoading = true;
    loadingPromise = (async () => {
      try {
        console.log("Creating new FFmpeg instance");
        const ffmpegInstance = new FFmpeg();

        try {
          console.log("Loading FFmpeg from CDN...");

          // 使用带版本号的CDN URL作为首选方案
          const cdnVersion = "0.12.10"; // 与本地使用相同的版本

          await ffmpegInstance.load({
            coreURL: `https://unpkg.com/@ffmpeg/core@${cdnVersion}/dist/umd/ffmpeg-core.js`,
            wasmURL: `https://unpkg.com/@ffmpeg/core@${cdnVersion}/dist/umd/ffmpeg-core.wasm`,
            // 注意：FFmpeg 0.12.10 版本不需要 worker.js 文件
          });

          console.log("FFmpeg loaded from CDN successfully");
        } catch (cdnLoadError) {
          // 记录详细的错误信息
          console.error("Failed to load FFmpeg from CDN:", cdnLoadError);

          // 记录错误信息
          const errorStr = String(cdnLoadError);
          trackEvent("ffmpeg_cdn_load_failed", {
            error: errorStr,
            errorType: cdnLoadError.name || "Unknown",
            userAgent: navigator.userAgent,
            browserInfo: getBrowserInfo(),
          });

          // 如果CDN加载失败，尝试从本地文件加载作为备选方案
          console.log("Attempting to load FFmpeg from local files...");

          try {
            // 直接使用URL，不转换为Blob URL
            // 这样浏览器可以正常缓存这些文件
            await ffmpegInstance.load({
              coreURL: `/ffmpeg/ffmpeg-core.js?v=${FFMPEG_VERSION}`,
              wasmURL: `/ffmpeg/ffmpeg-core.wasm?v=${FFMPEG_VERSION}`,
              // 注意：FFmpeg 0.12.10 版本不需要 worker.js 文件
            });
            console.log("FFmpeg loaded from local files successfully");
          } catch (localLoadError) {
            // 记录本地加载失败的详细信息
            console.error(
              "Failed to load FFmpeg from local files:",
              localLoadError
            );

            // 记录错误信息
            const localErrorStr = String(localLoadError);
            trackEvent("ffmpeg_local_load_failed", {
              error: localErrorStr,
              errorType: localLoadError.name || "Unknown",
              userAgent: navigator.userAgent,
              browserInfo: getBrowserInfo(),
            });

            // 重新抛出错误，让外层捕获
            throw localLoadError;
          }
        }

        // 保存全局实例并设置就绪标志
        globalFFmpegInstance = ffmpegInstance;
        isGlobalFFmpegReady = true; // 设置全局就绪标志
        return ffmpegInstance;
      } catch (err) {
        console.error("Failed to initialize FFmpeg:", err);
        trackEvent("ffmpeg_initialize_failed", {
          error: String(err),
        });
        throw err;
      } finally {
        isFFmpegLoading = false;
      }
    })();

    try {
      const ffmpegInstance = await loadingPromise;
      ffmpegInstanceRef.current = ffmpegInstance;
      setFFmpeg(ffmpegInstance);
      setIsFFmpegReady(true);
      return true;
    } catch (err) {
      setError("Failed to initialize media processor");
      setIsFFmpegReady(false);
      return false;
    }
  };

  useEffect(() => {
    const initFFmpeg = async () => {
      if (!ffmpeg) {
        await initializeFFmpeg();
      }
    };

    initFFmpeg();

    // 清理函数，在组件卸载时执行
    return () => {
      // 不再终止FFmpeg实例，因为它是全局共享的
      // 只有在没有正在处理文件时才清除本地引用
      if (!isProcessingFileRef.current) {
        console.log("Component unmounting, clearing local FFmpeg reference", {
          hadLocalRef: !!ffmpegInstanceRef.current,
          isProcessing: processing,
          isProcessingFile: isProcessingFileRef.current,
        });
        ffmpegInstanceRef.current = null;
      } else {
        console.log(
          "Component unmounting but file processing in progress, keeping FFmpeg reference",
          {
            hadLocalRef: !!ffmpegInstanceRef.current,
            isProcessing: processing,
            isProcessingFile: isProcessingFileRef.current,
          }
        );
      }
    };
  }, [ffmpeg, processing]);

  const interruptProcessing = async () => {
    isInterruptedRef.current = true;
    if (abortControllerRef.current) {
      console.log("Aborting FFmpeg processing...");
      abortControllerRef.current.abort();
      setProcessing(false);
      setProcessingType(null);
      setProgress(0);

      // 不再终止FFmpeg实例，只中止当前处理
      // 这样可以避免破坏全局实例

      // 如果需要重新初始化，可以使用现有的全局实例
      if (
        (!ffmpegInstanceRef.current || !isFFmpegReady) &&
        globalFFmpegInstance &&
        isGlobalFFmpegReady
      ) {
        // 如果全局实例可用，直接使用
        ffmpegInstanceRef.current = globalFFmpegInstance;
        setFFmpeg(globalFFmpegInstance);
        setIsFFmpegReady(true);
        console.log("Reused global FFmpeg instance after interruption");
      } else if (!ffmpegInstanceRef.current || !isFFmpegReady) {
        // 否则尝试重新初始化
        try {
          console.log(
            "Attempting to reinitialize FFmpeg after interruption..."
          );
          await initializeFFmpeg();
        } catch (err) {
          console.error("FFmpeg reinitialization failed:", err);
          setError("Failed to reinitialize media processor");
        }
      }
    }
  };

  const processFile = async (file, processorConfig) => {
    if (!file) return file;

    // 在开始处理前，尝试恢复本地引用（防止同步问题导致的引用丢失）
    if (
      !ffmpegInstanceRef.current &&
      globalFFmpegInstance &&
      isGlobalFFmpegReady
    ) {
      console.log("Recovering lost local FFmpeg reference", {
        hasGlobalInstance: !!globalFFmpegInstance,
        isGlobalReady: isGlobalFFmpegReady,
        fileName: file?.name,
      });
      ffmpegInstanceRef.current = globalFFmpegInstance;
      setFFmpeg(globalFFmpegInstance);
      setIsFFmpegReady(true);
    }

    // 检查文件大小是否超过 WebAssembly 的 2GB 限制
    const MAX_FILE_SIZE = 2 * 1024 * 1024 * 1024; // 2GB in bytes
    if (file.size > MAX_FILE_SIZE) {
      console.warn(
        `File size (${(file.size / (1024 * 1024)).toFixed(2)} MB) exceeds the 2GB WebAssembly limit. Skipping FFmpeg processing.`
      );
      trackEvent("ffmpeg_file_too_large", {
        fileSize: file.size,
        fileName: file.name,
        fileType: file.type,
      });
      // 直接返回原始文件，不尝试处理，但不中断上传流程
      return file;
    }

    // 检查浏览器是否支持 SIMD
    const simdSupported = await detectSIMDSupport();
    if (!simdSupported) {
      console.warn(
        "Browser does not support WebAssembly SIMD. Skipping FFmpeg processing."
      );
      trackEvent("ffmpeg_simd_not_supported_process", {
        userAgent: navigator.userAgent,
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
      });
      // 直接返回原始文件，不尝试处理，但不中断上传流程
      return file;
    }

    // 首先检查全局实例是否已就绪
    if (globalFFmpegInstance && isGlobalFFmpegReady) {
      // 关键修复：始终同步本地引用，即使它已经存在
      // 这确保本地引用指向正确的全局实例
      if (ffmpegInstanceRef.current !== globalFFmpegInstance) {
        console.log("Syncing local reference with global FFmpeg instance", {
          hadLocalRef: !!ffmpegInstanceRef.current,
          localRefEqualsGlobal:
            ffmpegInstanceRef.current === globalFFmpegInstance,
        });
        ffmpegInstanceRef.current = globalFFmpegInstance;
        setFFmpeg(globalFFmpegInstance);
        setIsFFmpegReady(true);
      }
      console.log("FFmpeg is ready (global instance)", {
        hasLocalRef: !!ffmpegInstanceRef.current,
        hasGlobalInstance: !!globalFFmpegInstance,
        isGlobalReady: isGlobalFFmpegReady,
        isLocalReady: isFFmpegReady,
        referencesMatch: ffmpegInstanceRef.current === globalFFmpegInstance,
      });
    }
    // 如果全局实例不可用或本地状态不就绪，尝试初始化
    else if (!isFFmpegReady || !ffmpegInstanceRef.current) {
      console.log("FFmpeg not ready, attempting to initialize...", {
        hasLocalRef: !!ffmpegInstanceRef.current,
        hasGlobalInstance: !!globalFFmpegInstance,
        isGlobalReady: isGlobalFFmpegReady,
        isLocalReady: isFFmpegReady,
      });
      const success = await initializeFFmpeg();
      if (!success) {
        setError("Media processor is not ready. Please try again.");
        trackEvent("ffmpeg_process_file_failed", {
          reason: "initialization_failed",
          hasFFmpeg: !!ffmpegInstanceRef.current,
          isFFmpegReady,
          fileName: file?.name,
          fileType: file?.type,
        });
        return file;
      }
    } else {
      console.log("FFmpeg is ready (local instance)", {
        hasLocalRef: !!ffmpegInstanceRef.current,
        isLocalReady: isFFmpegReady,
      });
    }

    // 再次检查FFmpeg实例是否可用
    if (!ffmpegInstanceRef.current) {
      console.error(
        "FFmpeg instance is still not available after initialization"
      );
      setError("Media processor is not ready. Please try again.");
      trackEvent("ffmpeg_process_file_failed", {
        reason: "instance_null_after_init",
        fileName: file?.name,
        fileType: file?.type,
      });
      return file;
    }

    abortControllerRef.current = new AbortController();
    const { signal } = abortControllerRef.current;

    let handleProgress;

    try {
      setProcessing(true);
      setProcessingType(
        processorConfig.outputType.includes("audio") ? "audio" : "video"
      );
      setProgress(0);
      setError(null);
      isInterruptedRef.current = false;
      isProcessingFileRef.current = true; // 设置文件处理标志

      const { command, outputFilename, outputType } = processorConfig;

      // 在每个关键操作前再次检查FFmpeg实例是否可用
      if (!ffmpegInstanceRef.current) {
        console.error("FFmpeg instance became null before file processing", {
          hasGlobalInstance: !!globalFFmpegInstance,
          isGlobalReady: isGlobalFFmpegReady,
          isLocalReady: isFFmpegReady,
        });
        trackEvent("ffmpeg_instance_null", {
          stage: "before_arrayBuffer",
          fileName: file?.name,
          hasGlobalInstance: !!globalFFmpegInstance,
          isGlobalReady: isGlobalFFmpegReady,
          isLocalReady: isFFmpegReady,
        });
        throw new Error(
          "Media processor became unavailable. Please try again."
        );
      }

      console.log("About to process file", {
        fileName: file?.name,
        fileSize: file?.size,
        hasFFmpegRef: !!ffmpegInstanceRef.current,
        hasGlobalInstance: !!globalFFmpegInstance,
        isGlobalReady: isGlobalFFmpegReady,
      });

      const fileData = await file.arrayBuffer();

      // 再次检查FFmpeg实例是否可用，如果为null则尝试恢复
      if (!ffmpegInstanceRef.current) {
        console.error("FFmpeg instance became null after arrayBuffer", {
          hasGlobalInstance: !!globalFFmpegInstance,
          isGlobalReady: isGlobalFFmpegReady,
        });

        // 尝试从全局实例恢复
        if (globalFFmpegInstance && isGlobalFFmpegReady) {
          console.log("Attempting to recover FFmpeg instance from global");
          ffmpegInstanceRef.current = globalFFmpegInstance;
          setFFmpeg(globalFFmpegInstance);
          setIsFFmpegReady(true);
        } else {
          trackEvent("ffmpeg_instance_null", {
            stage: "after_arrayBuffer",
            fileName: file?.name,
            hasGlobalInstance: !!globalFFmpegInstance,
            isGlobalReady: isGlobalFFmpegReady,
          });
          throw new Error(
            "Media processor became unavailable. Please try again."
          );
        }
      }

      console.log("About to call writeFile", {
        hasFFmpegRef: !!ffmpegInstanceRef.current,
        dataSize: fileData.byteLength,
      });

      try {
        await ffmpegInstanceRef.current.writeFile(
          "input_file",
          new Uint8Array(fileData),
          { signal }
        );
        console.log("writeFile completed successfully");
      } catch (writeError) {
        console.error("Error writing file to FFmpeg:", writeError, {
          hasFFmpegRef: !!ffmpegInstanceRef.current,
          errorType: writeError?.name,
          errorMessage: writeError?.message,
        });
        throw new Error("Failed to process media file. Please try again.");
      }

      handleProgress = ({ progress }) => {
        if (isInterruptedRef.current) {
          console.log("Processing interrupted");
          ffmpegInstanceRef.current?.off("progress", handleProgress);
          return;
        }
        setProgress(Math.round(progress * 100));
      };

      if (ffmpegInstanceRef.current) {
        ffmpegInstanceRef.current.on("progress", handleProgress);
      }

      // 再次检查FFmpeg实例是否可用
      if (!ffmpegInstanceRef.current) {
        throw new Error(
          "Media processor became unavailable. Please try again."
        );
      }

      await ffmpegInstanceRef.current.exec(command, undefined, { signal });

      // 再次检查FFmpeg实例是否可用
      if (!ffmpegInstanceRef.current) {
        throw new Error(
          "Media processor became unavailable. Please try again."
        );
      }

      const outputData = await ffmpegInstanceRef.current.readFile(
        outputFilename,
        undefined,
        { signal }
      );
      const processedBlob = new Blob([outputData], { type: outputType });

      return new File(
        [processedBlob],
        `${file.name.split(".")[0]}.${outputType.split("/")[1]}`,
        {
          type: outputType,
          lastModified: Date.now(),
        }
      );
    } catch (err) {
      console.error("FFmpeg processing error:", err);
      throw err;
    } finally {
      if (ffmpegInstanceRef.current && handleProgress) {
        try {
          ffmpegInstanceRef.current.off("progress", handleProgress);
        } catch (e) {
          console.warn("Error removing progress handler:", e);
        }
      }
      abortControllerRef.current = null;
      isProcessingFileRef.current = false; // 清除文件处理标志
      if (!isInterruptedRef.current) {
        setProcessing(false);
        setProcessingType(null);
        setProgress(0);
      }
    }
  };

  return {
    processFile,
    interruptProcessing,
    processing,
    processingType,
    progress,
    error,
    setError,
    isFFmpegReady,
  };
};
