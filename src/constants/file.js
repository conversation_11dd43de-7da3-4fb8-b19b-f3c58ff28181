export const FILE_STATUS = {
  UPLOADING: "uploading",
  UPLOADED: "uploaded",
  PREPROCESSING: "preprocessing",
  PREPROCESSING_FAILED: "preprocessing_failed",
  PROCESSING: "processing",
  PARTIALLY_COMPLETED: "partially_completed",
  COMPLETED_WITH_ERRORS: "completed_with_errors",
  COMPLETED: "completed",
  FAILED: "failed",
};

// File status utility functions
export const FILE_STATUS_UTILS = {
  // Statuses that allow file deletion
  DELETABLE_STATUSES: [
    FILE_STATUS.UPLOADING,
    FILE_STATUS.UPLOADED,
    FILE_STATUS.PREPROCESSING_FAILED,
    FILE_STATUS.PARTIALLY_COMPLETED,
    FILE_STATUS.COMPLETED,
    FILE_STATUS.COMPLETED_WITH_ERRORS,
    FILE_STATUS.FAILED,
  ],

  // Check if file can be deleted
  canDelete: (status) => FILE_STATUS_UTILS.DELETABLE_STATUSES.includes(status),
};

// MIME 类型到扩展名的映射 - 主要数据源
export const MIME_TO_EXTENSION = {
  "audio/mpeg": "mp3",
  "audio/mp4": "m4a",
  "audio/x-m4a": "m4a",
  "audio/wav": "wav",
  "audio/x-wav": "wav",
  "audio/webm": "webm",
  "audio/weba": "weba",
  "video/mp4": "mp4",
  "video/webm": "webm",
  "video/quicktime": "mov",
  "video/mpeg": "mpg",
  "video/3gpp": "3gp",
  "audio/vnd.dlna.adts": "aac",
  "audio/aac": "aac",
  "audio/ogg": "ogg",
  "audio/oga": "oga",
  "audio/opus": "opus",
  "audio/flac": "flac",
  "audio/x-flac": "flac",
};

// 从映射中生成支持的 MIME 类型列表
export const VALID_FILE_TYPES = Object.keys(MIME_TO_EXTENSION);

// 从映射中生成支持的文件扩展名列表（去重）
export const SUPPORTED_AUDIO_FORMATS = [
  ...new Set(Object.values(MIME_TO_EXTENSION)),
  "mp2", // 手动添加 mp2 扩展名支持
].sort();

export const SUPPORTED_FORMATS_WITH_YOUTUBE = [
  "YouTube Link",
  ...SUPPORTED_AUDIO_FORMATS,
];
export const SUPPORTED_FORMATS_DISPLAY = SUPPORTED_AUDIO_FORMATS.join("/");
export const SUPPORTED_FORMATS_ACCEPT = SUPPORTED_AUDIO_FORMATS.map(
  (format) => `.${format}`
).join(",");

// 为 FAQ 格式化文本
export const getFormatsDisplayText = () => {
  const formats = [...SUPPORTED_AUDIO_FORMATS];
  const lastFormat = formats.pop();
  return `${formats.join(", ")} and ${lastFormat}`;
};

// 免费用户限制常量
export const FREE_USER_LIMITS = {
  DAILY_FILE_COUNT: 3, // 免费用户每日文件上传限制
  DURATION_MINUTES: 120, // 免费用户每月转录时长限制（分钟）
};

// 匿名用户限制常量
export const ANONYMOUS_USER_LIMITS = {
  TRANSCRIPTION_MINUTES: 60, // 匿名用户转录时长限制（分钟）
  FILE_UPLOAD_COUNT: 1, // 匿名用户文件上传次数限制
  YOUTUBE_TRANSCRIPTION_COUNT: 1, // 匿名用户YouTube转录次数限制
};

// 为了向后兼容，导出单独的常量
export const ANONYMOUS_TRANSCRIPTION_MINUTES_LIMIT =
  ANONYMOUS_USER_LIMITS.TRANSCRIPTION_MINUTES;
