/**
 * RemainingMinutes Component
 *
 * Displays user's plan information including:
 * - Total remaining minutes across all plans
 * - Individual plan cards for each active plan (Free, Subscription, One-time)
 * - Plan-specific details (minutes, reset/expiry dates)
 *
 * Supports multiple concurrent plans with different types and priorities.
 * Uses the /entitlements API endpoint for data.
 */

import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import { useState, useEffect } from "react";
import { subscriptionService } from "@/services/api/subscriptionService";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";
import { format } from "date-fns";
import {
  Clock,
  ArrowRight,
  Calendar,
  Timer,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useEntitlementsStore } from "@/stores/useEntitlementsStore";
import { useAuthStore } from "@/stores/useAuthStore";
import { safeOpenUrl } from "@/lib/browserUtils";

// Plan type constants
const PLAN_TYPES = {
  FREE: "free",
  SUBSCRIPTION: "subscription",
  ONE_TIME: "one-time",
  BONUS: "bonus",
  LTD: "ltd",
};

// Centralized color mapping for plan types
const PLAN_COLORS = {
  [PLAN_TYPES.FREE]: {
    badge: "bg-gray-500",
    badgeHover: "hover:bg-gray-600",
    progressBar: "bg-gray-500",
    border: "border-gray-200",
  },
  [PLAN_TYPES.SUBSCRIPTION]: {
    badge: "bg-indigo-500",
    badgeHover: "hover:bg-indigo-600",
    progressBar: "bg-indigo-500",
    border: "border-indigo-500/30",
  },
  [PLAN_TYPES.ONE_TIME]: {
    badge: "bg-green-500",
    badgeHover: "hover:bg-green-600",
    progressBar: "bg-green-600",
    border: "border-green-500/30",
  },
  [PLAN_TYPES.BONUS]: {
    badge: "bg-purple-500",
    badgeHover: "hover:bg-purple-600",
    progressBar: "bg-purple-500",
    border: "border-purple-500/30",
  },
  [PLAN_TYPES.LTD]: {
    badge: "bg-amber-500",
    badgeHover: "hover:bg-amber-600",
    progressBar: "bg-amber-600",
    border: "border-amber-500/30",
  },
};

// Plan Card Component
function PlanCard({ plan, onManagePlan }) {
  const t = useTranslations("dashboard.remainingMinutes");

  // Get plan type label based on plan type
  const getPlanTypeLabel = (type) => {
    switch (type) {
      case PLAN_TYPES.FREE:
        return "Free";
      case PLAN_TYPES.SUBSCRIPTION:
        return "Subscription";
      case PLAN_TYPES.ONE_TIME:
        return "One-time";
      case PLAN_TYPES.BONUS:
        return "Bonus";
      case PLAN_TYPES.LTD:
        return "AppSumo";
      default:
        return "Free";
    }
  };

  // Get badge classes based on plan type
  const getBadgeClass = (type) => {
    const colorSet = PLAN_COLORS[type] || PLAN_COLORS[PLAN_TYPES.FREE];
    return cn(
      "text-[10px] px-1.5 py-0.5 leading-none min-w-[50px] text-center inline-block text-white rounded-md whitespace-nowrap",
      colorSet.badge,
      colorSet.badgeHover
    );
  };

  // Get progress bar class based on plan type
  const getProgressBarClass = (type) => {
    const colorSet = PLAN_COLORS[type] || PLAN_COLORS[PLAN_TYPES.FREE];
    return cn(
      "h-full rounded-full transition-all duration-500 ease-in-out",
      colorSet.progressBar
    );
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString || isNaN(new Date(dateString).getTime())) return null;

    // 直接使用本地时区解析日期
    // 这样会自动将UTC时间转换为用户的本地时区
    const date = new Date(dateString);

    // 使用format函数格式化日期
    return format(date, "MMM dd, yyyy");
  };

  // Get action button text based on plan type
  const getActionButtonText = (planType) => {
    switch (planType) {
      case PLAN_TYPES.FREE:
        return t("upgradeNow");
      case PLAN_TYPES.SUBSCRIPTION:
        return t("managePlan");
      case PLAN_TYPES.ONE_TIME:
        return t("buyMore");
      case PLAN_TYPES.LTD:
        return t("managePlan");
      default:
        // Default to upgradeNow to be consistent with onManagePlan function
        return t("upgradeNow");
    }
  };

  const formattedResetTime = formatDate(plan.resetsAt);
  const formattedExpiryTime = formatDate(plan.expiresAt);

  // Get border class based on plan type
  const getBorderClass = (type) => {
    const colorSet = PLAN_COLORS[type] || PLAN_COLORS[PLAN_TYPES.FREE];
    return cn("mb-3 overflow-hidden rounded-lg", colorSet.border);
  };

  return (
    <Card className={getBorderClass(plan.type)}>
      {/* Main Card Content - Single Row Layout */}
      <div className="py-3 px-4">
        {/* Top Row with Plan Info and Minutes */}
        <div className="flex justify-between mb-3">
          {/* Left Side - Plan Type and Name */}
          <div className="flex flex-col min-w-0 gap-1 pr-2 max-w-[60%]">
            <Badge className={getBadgeClass(plan.type)}>
              {getPlanTypeLabel(plan.type)}
            </Badge>
            <span className="font-medium text-sm break-words">{plan.name}</span>
          </div>

          {/* Right Side - Minutes Counter */}
          <div className="flex flex-col items-end gap-1 flex-shrink-0">
            <div className="flex items-center gap-1">
              <Timer className="w-4 h-4 text-gray-500" />
              <span className="font-medium text-sm whitespace-nowrap">
                {plan.remainingMinutes} / {plan.totalMinutes}
              </span>
            </div>
            {/* Small Progress Bar */}
            <div className="w-24 h-1 bg-gray-100 rounded-full overflow-hidden">
              <div
                className={getProgressBarClass(plan.type)}
                style={{
                  width: `${(plan.remainingMinutes / plan.totalMinutes) * 100}%`,
                }}
              />
            </div>
          </div>
        </div>

        {/* Bottom Row with Expiry/Reset Date and Action Button */}
        <div className="flex justify-between items-center">
          {/* Left Side - Expiry/Reset Date */}
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <Calendar className="w-3 h-3 flex-shrink-0" />
            {formattedResetTime && (
              <span className="whitespace-nowrap">
                {t("resetsOn", { date: formattedResetTime })}
              </span>
            )}
            {formattedExpiryTime && (
              <span className="whitespace-nowrap">
                {t("expiresOn", { date: formattedExpiryTime })}
              </span>
            )}
          </div>

          {/* Right Side - Action Button (if applicable) */}
          {plan.type !== PLAN_TYPES.BONUS && (
            <Button
              variant="link"
              className="text-custom-bg p-0 h-auto text-xs font-medium hover:text-custom-bg-600 hover:no-underline group flex items-center gap-1 flex-shrink-0"
              onClick={() => onManagePlan(plan)}
            >
              {getActionButtonText(plan.type)}
              <ArrowRight className="w-3 h-3 transition-transform group-hover:translate-x-0.5" />
            </Button>
          )}
        </div>
      </div>
    </Card>
  );
}

// Usage Summary Component (formerly TotalMinutes)
function UsageSummary({
  totalRemainingMinutes,
  isExpanded,
  summary,
  isAppSumoUser,
}) {
  const t = useTranslations("dashboard.remainingMinutes");

  // Get storage data from API summary (already in GB)
  const totalStorageGB = summary?.totalStorage || 0;
  const consumedStorageGB = summary?.consumedStorage || 0;
  // Fix floating point precision issues by using toFixed to limit decimal places
  const availableStorageGB = parseFloat(
    (totalStorageGB - consumedStorageGB).toFixed(2)
  );

  const storageData = {
    totalGB: totalStorageGB,
    usedGB: consumedStorageGB,
    availableGB: availableStorageGB,
  };

  return (
    <Card className="w-full bg-white border border-gray-200 rounded-lg shadow-sm hover:bg-gray-50/50 transition-colors cursor-pointer overflow-hidden">
      <div className="p-6">
        {/* Header with Title and Expand Button */}
        <div className="flex items-center justify-between mb-5">
          <h2 className="text-lg font-semibold">{t("usageSummary")}</h2>
          <div className="cursor-pointer text-gray-400 transition-transform duration-200">
            {isExpanded ? (
              <ChevronUp className="w-4 h-4" />
            ) : (
              <ChevronDown className="w-4 h-4" />
            )}
          </div>
        </div>

        {/* 紧凑仪表盘布局 */}
        <div className={isAppSumoUser ? "space-y-5" : ""}>
          {/* Minutes Section */}
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Clock className="w-4 h-4 text-custom-bg mr-2" />
              <span className="text-base font-semibold text-custom-bg">
                {totalRemainingMinutes} {t("minutesRemaining")}
              </span>
            </div>
            <div className="flex items-center justify-between text-xs text-gray-600 mb-1.5">
              <span>
                {t("used")}: {summary?.consumedCredits || 0}
              </span>
              <span>
                {t("total")}: {summary?.totalCredits || 0}
              </span>
            </div>
            <div className="h-2.5 w-full bg-custom-bg/10 rounded-full overflow-hidden">
              <div
                className="h-full bg-custom-bg rounded-full transition-all duration-500 ease-in-out"
                style={{
                  width: `${summary?.totalCredits ? ((summary.consumedCredits || 0) / summary.totalCredits) * 100 : 0}%`,
                }}
              />
            </div>
          </div>

          {/* Storage Section - Only show for AppSumo users */}
          {isAppSumoUser && (
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <svg
                  className="w-4 h-4 text-green-600 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7M4 7c0-2.21 1.79-4 4-4h8c2.21 0 4 1.79 4 4M4 7h16"
                  />
                </svg>
                <span className="text-base font-semibold text-green-900">
                  {storageData.availableGB} {t("gbAvailable")}
                </span>
              </div>
              <div className="flex items-center justify-between text-xs text-gray-600 mb-1.5">
                <span>
                  {t("storageUsed")}: {storageData.usedGB} GB
                </span>
                <span>
                  {t("storageTotal")}: {storageData.totalGB} GB
                </span>
              </div>
              <div className="h-2.5 w-full bg-green-100 rounded-full overflow-hidden">
                <div
                  className="h-full bg-green-600 rounded-full transition-all duration-500 ease-in-out"
                  style={{
                    width: `${storageData.totalGB ? (storageData.usedGB / storageData.totalGB) * 100 : 0}%`,
                  }}
                />
              </div>
            </div>
          )}
        </div>

        {!isExpanded && (
          <div className="mt-5 text-center text-xs text-gray-500">
            {t("clickForDetails")}
          </div>
        )}
      </div>
    </Card>
  );
}

// Main Component
export function RemainingMinutes() {
  const { entitlements, summary, loading, error, fetchEntitlements } =
    useEntitlementsStore();
  const { user } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  const { openDialog } = useUpgradeDialogStore();
  const [isAccordionOpen, setIsAccordionOpen] = useState(false);
  const t = useTranslations("dashboard.remainingMinutes");

  // Check if user is AppSumo user
  const isAppSumoUser = user?.primaryPlanDetail?.isAppsumo || false;

  // Fetch entitlements data when component mounts
  useEffect(() => {
    fetchEntitlements();
  }, [fetchEntitlements]);

  // Map source type to plan type
  const mapSourceTypeToPlanType = (source) => {
    if (!source || !source.type) return PLAN_TYPES.FREE;

    switch (source.type) {
      case "free_plan":
        return PLAN_TYPES.FREE;
      case "subscription":
        return PLAN_TYPES.SUBSCRIPTION;
      case "one_time":
        return PLAN_TYPES.ONE_TIME;
      case "bonus":
        return PLAN_TYPES.BONUS;
      case "ltd":
        return PLAN_TYPES.LTD;
      // Add more mappings as needed
      default:
        return PLAN_TYPES.FREE;
    }
  };

  // Get plan name based on source information
  const getPlanName = (source) => {
    if (!source || !source.type) return "Unknown Plan";

    // If plan information is available, use it
    if (source.plan) {
      // Use tier or name from plan
      if (source.plan.tier) {
        return source.plan.tier;
      }
      // Fallback to plan name
      return source.plan.name;
    }

    // If no plan info, use source type
    switch (source.type) {
      case "bonus":
        return "Bonus";
      default:
        return source.type;
    }
  };

  // Get plan priority based on source type
  const getPlanPriority = (source) => {
    if (!source || !source.type) return 6; // Lowest priority for unknown

    switch (source.type) {
      case "free_plan":
        return 5;
      case "subscription":
        return 3;
      case "one_time":
        return 4;
      case "bonus":
        return 1; // Highest priority for bonus
      case "ltd":
        return 2; // High priority for lifetime deals
      default:
        return 6; // Lowest priority for unknown types
    }
  };

  // Transform entitlements data to plan objects
  const userPlans =
    entitlements?.map((entitlement) => ({
      id: entitlement.id.toString(),
      name: getPlanName(entitlement.source),
      type: mapSourceTypeToPlanType(entitlement.source),
      totalMinutes: entitlement.totalCredits,
      remainingMinutes: entitlement.remainingCredits,
      // For recurring plans, use validUntil as resetsAt
      resetsAt: entitlement.isRecurring ? entitlement.validUntil : null,
      // For non-recurring plans, use validUntil as expiresAt
      expiresAt: !entitlement.isRecurring ? entitlement.validUntil : null,
      priority: getPlanPriority(entitlement.source),
      source: entitlement.source,
      isRecurring: entitlement.isRecurring,
      // Store plan details if available
      plan: entitlement.source?.plan || null,
    })) || [];

  // Sort plans by priority (lower number = higher priority)
  const sortedPlans = [...userPlans].sort((a, b) => a.priority - b.priority);

  // Get total remaining minutes from summary
  const totalRemainingMinutes = summary?.remainingCredits || 0;

  const handleManagePlan = async (plan) => {
    // For Free Plan and One-time plans - show upgrade dialog
    if (plan.type === PLAN_TYPES.FREE || plan.type === PLAN_TYPES.ONE_TIME) {
      // Set dialog type based on plan type
      const dialogType =
        plan.type === PLAN_TYPES.ONE_TIME ? "onetime" : "yearly";
      openDialog({
        source: "remaining_minutes",
        defaultPlanType: dialogType,
      });
      return;
    }

    // For LTD plans, redirect to AppSumo account products page
    if (plan.type === PLAN_TYPES.LTD) {
      safeOpenUrl("https://appsumo.com/account/products/");
      return;
    }

    // Only subscription plans can be managed through customer portal
    if (plan.type === PLAN_TYPES.SUBSCRIPTION) {
      setIsLoading(true);
      try {
        const response = await subscriptionService.createCustomerPortal();
        const url = response.data.url;
        if (url) {
          safeOpenUrl(url);
        }
      } catch (error) {
        console.error("Failed to manage subscription:", error);
      } finally {
        setIsLoading(false);
      }
      return;
    }

    // Default case: treat as FREE plan and show upgrade dialog
    // This ensures consistency with getActionButtonText function
    openDialog({
      source: "remaining_minutes",
      defaultPlanType: "yearly",
    });
  };

  // Pass loading state to plan cards
  const plansWithLoadingState = sortedPlans.map((plan) => ({
    ...plan,
    isButtonDisabled: isLoading,
  }));

  // Show loading state
  if (loading) {
    return (
      <div className="border border-gray-200 rounded-xl p-4 space-y-2 bg-gray-50/50 flex items-center justify-center">
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-custom-bg mx-auto mb-2"></div>
          <p className="text-gray-500 text-sm">{t("loading")}</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    console.error("Error in RemainingMinutes:", error);
    return (
      <div className="border border-red-200 rounded-xl p-4 space-y-2 bg-red-50/50">
        <div className="text-center py-4">
          <p className="text-red-500">{t("errorLoading")}</p>
          <Button
            variant="link"
            className="text-custom-bg mt-2 font-medium hover:text-custom-bg-600"
            onClick={() => fetchEntitlements()}
          >
            {t("tryAgain")}
          </Button>
        </div>
      </div>
    );
  }

  // Show empty state if no entitlements
  if (!entitlements || entitlements.length === 0) {
    return (
      <div className="border border-gray-200 rounded-xl p-4 space-y-2 bg-gray-50/50">
        <div className="text-center py-4">
          <p className="text-gray-500">{t("noPlans")}</p>
          <Button
            variant="link"
            className="text-custom-bg mt-2 font-medium hover:text-custom-bg-600"
            onClick={() => {
              openDialog({
                source: "remaining_minutes",
                defaultPlanType: "yearly",
              });
            }}
          >
            {t("upgradeNow")}
          </Button>
        </div>
      </div>
    );
  }

  // Use the accordion open state defined above

  return (
    <>
      {/* Accordion for the entire component */}
      <Accordion
        type="single"
        collapsible
        className="w-full"
        onValueChange={(value) => setIsAccordionOpen(!!value)}
      >
        <AccordionItem value="entitlements" className="border-none">
          {/* Usage Summary - Always visible and acts as trigger */}
          <AccordionTrigger className="p-0 hover:no-underline w-full [&>svg]:hidden data-[state=open]:pb-0">
            <div className="w-full">
              <UsageSummary
                totalRemainingMinutes={totalRemainingMinutes}
                isExpanded={isAccordionOpen}
                summary={summary}
                isAppSumoUser={isAppSumoUser}
              />
            </div>
          </AccordionTrigger>

          {/* Plan Cards - Only visible when expanded */}
          <AccordionContent className="pt-3 pb-4">
            <div className="space-y-3 px-1">
              {plansWithLoadingState.map((plan) => (
                <PlanCard
                  key={plan.id}
                  plan={plan}
                  onManagePlan={handleManagePlan}
                />
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </>
  );
}
