"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Download } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import { TASK_STATUS } from "@/constants/task";

import { Button } from "@/components/ui/button";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ExportDialogContent } from "@/components/Dashboard/DashboardV2/components/ExportDialog";
import GuestModeDialog from "@/components/Dashboard/GuestModeDialog";

export default function ExportFileMenu({
  fileId,
  planConfig,
  duration,
  isSharedPage = false,
  transcriptionType = null,
  isAnonymous = false,
  insufficientMinutes = 0,
  taskStatuses,
  exportSource = "transcription_detail", // 默认为转录详细页面
}) {
  const t = useTranslations("dashboard.export");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [showGuestModeDialog, setShowGuestModeDialog] = useState(false);

  const isMobile = useMediaQuery("(max-width: 768px)");

  // 使用 transcription.taskStatuses?.transcription === "completed" 来判断转录任务是否已完成
  const isTranscriptionComplete =
    taskStatuses?.transcription === TASK_STATUS.COMPLETED;

  const handleTriggerClick = (e) => {
    if (isAnonymous) {
      e.preventDefault(); // 阻止 Dialog 打开
      setShowGuestModeDialog(true);
    }
  };

  const handleGuestModeDialogClose = () => {
    setShowGuestModeDialog(false);
  };

  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div>
                <DialogTrigger asChild onClick={handleTriggerClick}>
                  <Button
                    variant="outline"
                    className={`flex items-center ${
                      isTranscriptionComplete
                        ? "bg-custom-bg text-white hover:bg-custom-bg-600 hover:text-white focus:bg-custom-bg-700 focus:text-white"
                        : "bg-gray-300 text-gray-600 cursor-not-allowed"
                    }`}
                    size={isMobile ? "icon" : "default"}
                    disabled={!isTranscriptionComplete}
                  >
                    <Download className="w-4 h-4" />
                    {!isMobile && (
                      <span className="ml-2">{t("transcript")}</span>
                    )}
                  </Button>
                </DialogTrigger>
              </div>
            </TooltipTrigger>
            {!isTranscriptionComplete && (
              <TooltipContent>
                <p className="text-sm">{t("processing_export_disabled")}</p>
              </TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>

        <DialogContent className="sm:max-w-[500px] p-0">
          <DialogHeader className="p-6 pb-4">
            <DialogTitle className="text-xl font-semibold">
              {t("dialog_title")}
            </DialogTitle>
            <DialogDescription>{t("dialog_description")}</DialogDescription>
          </DialogHeader>

          <ExportDialogContent
            fileId={fileId}
            transcriptionType={transcriptionType}
            insufficientMinutes={insufficientMinutes}
            isSharedPage={isSharedPage}
            exportSource={exportSource}
            onExportComplete={() => setIsDialogOpen(false)}
            onCancel={() => setIsDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* 访客模式弹框 */}
      <GuestModeDialog
        isOpen={showGuestModeDialog}
        onClose={handleGuestModeDialogClose}
        source="transcript_export"
      />
    </>
  );
}
