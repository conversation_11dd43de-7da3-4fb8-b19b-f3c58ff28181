"use client";

import { useState, useEffect } from "react";
import { X } from "lucide-react";
import dynamic from "next/dynamic";
import styles from "./styles.module.css";
import { subscriptionService } from "@/services/api/subscriptionService";
import { useBanner } from "@/contexts/BannerContext";
import { FEATURES } from "@/config/features";
import { useAuthStore } from "@/stores/useAuthStore";
import { safeOpenUrl } from "@/lib/browserUtils";

const Countdown = dynamic(() => import("react-countdown"), {
  ssr: false,
});

const ChristmasBanner = () => {
  const { isBannerVisible, setIsBannerVisible } = useBanner();
  const [isLoading, setIsLoading] = useState(false);
  const { endDate, promoCode, discount } = FEATURES.CHRISTMAS_BANNER;
  const targetDate = new Date(endDate);
  const { user } = useAuthStore();

  useEffect(() => {
    if (user?.hasActiveSubscription) {
      setIsBannerVisible(false);
    }
  }, [user?.hasActiveSubscription, setIsBannerVisible]);

  if (!isBannerVisible) return null;

  const handleUpgradeClick = async () => {
    setIsLoading(true);
    try {
      const response = await subscriptionService.createCheckoutSession(
        "basic_yearly",
        promoCode,
        "subscription",
        "christmas_banner"
      );
      const url = response.data.url;
      if (url) {
        safeOpenUrl(url);
      }
    } catch (error) {
      console.error("Failed to create checkout session:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.banner}>
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        <div className="flex items-center space-x-2 text-sm sm:text-base">
          <Countdown
            date={targetDate}
            renderer={({ days, hours, minutes, seconds }) => (
              <div className="flex flex-col items-center">
                <div className="grid grid-cols-7 gap-0.5 text-md font-mono">
                  <div className="text-center">
                    {String(days).padStart(2, "0")}
                  </div>
                  <div className="text-center">:</div>
                  <div className="text-center">
                    {String(hours).padStart(2, "0")}
                  </div>
                  <div className="text-center">:</div>
                  <div className="text-center">
                    {String(minutes).padStart(2, "0")}
                  </div>
                  <div className="text-center">:</div>
                  <div className="text-center">
                    {String(seconds).padStart(2, "0")}
                  </div>
                </div>
                <div className="grid grid-cols-7 gap-0.5 text-[10px] opacity-80">
                  <div className="text-center">Days</div>
                  <div className="text-center"></div>
                  <div className="text-center">Hrs</div>
                  <div className="text-center"></div>
                  <div className="text-center">Mins</div>
                  <div className="text-center"></div>
                  <div className="text-center">Secs</div>
                </div>
              </div>
            )}
            onComplete={() => setIsBannerVisible(false)}
          />
        </div>

        <div className="flex-1 text-center px-4 flex items-center justify-center space-x-1">
          <span role="img" aria-label="santa" className="text-xl">
            🎅
          </span>
          <span className="font-medium">
            Happy Holidays! Dive into exclusive deals — Get
          </span>
          <span className="font-bold text-yellow-300">{discount}</span>
          <span>Use Code:</span>
          <code className={`${styles.promoCode} text-yellow-300`}>
            {promoCode}
          </code>
          <button
            onClick={handleUpgradeClick}
            disabled={isLoading}
            className={`${styles.checkoutButton} ${
              isLoading ? "opacity-75" : ""
            }`}
          >
            {isLoading ? "Loading..." : "Check it out"}
          </button>
          <span role="img" aria-label="christmas tree" className="text-xl">
            🎄
          </span>
        </div>

        <div className="flex items-center">
          <button
            onClick={() => setIsBannerVisible(false)}
            className={styles.closeButton}
            aria-label="Close banner"
          >
            <X size={20} />
          </button>
        </div>
      </div>

      <div className={styles.snowContainer}>
        <div className={styles.snow} />
      </div>
    </div>
  );
};

export default ChristmasBanner;
