"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Eye, EyeOff } from "lucide-react";
import Image from "next/image";
import { Link } from "@/components/Common/Link";
import Logo from "@/components/Logo";
import { useAuthFormUtils } from "./useAuthFormUtils";
import { AnimatePresence, motion } from "framer-motion";
import {
  isProblematicEmailDomain,
  PROBLEMATIC_EMAIL_DOMAINS,
} from "@/constants/emailDomains";

export default function SignUpForm() {
  // Reset the redirecting flag when component mounts
  if (typeof window !== "undefined") {
    window.isRedirectingToGoogle = false;
  }

  const {
    router,
    locale,
    t,
    isLoading,
    setIsLoading,
    error,
    setError,
    signUpWithEmail,
    validateEmail,
    validatePassword,
    checkEmailStatus,
    handleGoogleAuth,
  } = useAuthFormUtils();

  const [step, setStep] = useState(1);
  const [showPassword, setShowPassword] = useState(false);
  const [infoMessage, setInfoMessage] = useState("");
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    firstName: "",
    lastName: "",
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // Check for problematic email domains when email changes
    if (name === "email" && value) {
      if (isProblematicEmailDomain(value)) {
        const domains = PROBLEMATIC_EMAIL_DOMAINS.map(
          (domain) => `@${domain}`
        ).join(" and ");
        setInfoMessage(t("warnings.emailDeliveryIssue", { domains }));
      } else {
        setInfoMessage("");
      }
    }
  };

  const validateEmailStep = async () => {
    if (!formData.email) {
      setError(t("errors.emailRequired"));
      return false;
    }

    if (!validateEmail(formData.email)) {
      setError(t("errors.invalidEmail"));
      return false;
    }

    try {
      const data = await checkEmailStatus(formData.email);

      if (data.isDeactivated) {
        setError(t("errors.accountDeleted"));
        return false;
      }

      if (data.isDisposable) {
        setError(t("errors.disposableEmail"));
        return false;
      }

      if (data.isRegistered) {
        setError(t("errors.emailAlreadyRegistered"));
        return false;
      }

      // Check if it's a Gmail address (including googlemail.com which is an alias of gmail.com)
      const email = formData.email.toLowerCase();
      if (email.endsWith("@gmail.com") || email.endsWith("@googlemail.com")) {
        // Show info message and redirect to Google OAuth instead
        setInfoMessage(t("info.useGoogleAuth"));
        setError(""); // Clear any existing errors
        // Keep loading state true and don't reset it in the finally block
        // We'll set a flag to indicate we're redirecting to Google
        window.isRedirectingToGoogle = true;
        setTimeout(() => {
          handleGoogleAuth();
        }, 1500); // Give user time to read the message before redirecting
        return false;
      }
    } catch (error) {
      console.error("Error checking email status:", error);
      setError(t("errors.emailValidationError"));
      return false;
    }

    return true;
  };

  const validateFinalStep = () => {
    if (!formData.firstName || !formData.lastName || !formData.password) {
      setError(t("errors.fillAllFields"));
      return false;
    }

    if (!validatePassword(formData.password)) {
      setError(t("errors.passwordRequirements"));
      return false;
    }

    return true;
  };

  const handleContinue = async (e) => {
    e.preventDefault();
    setError("");
    setInfoMessage("");

    if (step === 1) {
      try {
        setIsLoading(true);
        const isValid = await validateEmailStep();
        if (isValid) {
          setStep(2);
        }
      } finally {
        // Only set loading to false if we're not in the process of redirecting to Google OAuth
        if (!window.isRedirectingToGoogle) {
          setIsLoading(false);
        }
      }
    }
  };

  const handleEmailAuth = async (e) => {
    e.preventDefault();
    setError("");
    setInfoMessage("");

    const isValid = validateFinalStep();
    if (!isValid) return;

    try {
      setIsLoading(true);
      const { error } = await signUpWithEmail(
        formData.email,
        formData.password,
        formData.firstName,
        formData.lastName,
        locale
      );

      if (error) {
        setError(error.message || t("errors.unexpectedError"));
        return;
      }

      router.push(
        `/auth/verify-email?email=${encodeURIComponent(formData.email)}`
      );
    } catch (error) {
      console.error("Auth error:", error);
      setError(t("errors.unexpectedError"));
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-sm p-6">
        <CardContent className="flex flex-col space-y-4">
          <div className="flex justify-center mb-6">
            <Logo />
          </div>

          <h2 className="text-2xl font-semibold text-center">
            {t("links.signUp")}
          </h2>

          <Button
            variant="outline"
            type="button"
            disabled={isLoading}
            onClick={handleGoogleAuth}
            className="w-full py-6 text-base font-medium border-2"
          >
            <span className="flex items-center">
              {isLoading && <Loader2 className="mr-2 h-5 w-5 animate-spin" />}
              {!isLoading && (
                <Image
                  src="/google_icon.svg"
                  alt="Google logo"
                  width={20}
                  height={20}
                  className="mr-2"
                  priority={true}
                />
              )}
            </span>
            {t("buttons.google")}
          </Button>

          <div className="relative flex items-center justify-center">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300"></div>
            </div>
            <div className="relative px-4 text-sm bg-white text-gray-500">
              {t("dividers.orSignUpEmail")}
            </div>
          </div>

          <AnimatePresence mode="wait">
            {step === 1 ? (
              <motion.form
                key="step1"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.2 }}
                onSubmit={handleContinue}
                className="space-y-4"
              >
                <div className="space-y-2">
                  <Label htmlFor="email">{t("email")}</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder={t("placeholders.email")}
                    value={formData.email}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    required
                    className="focus-visible:ring-custom-bg focus-visible:border-custom-bg"
                  />
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {infoMessage && (
                  <Alert
                    variant="info"
                    className="bg-blue-50 text-blue-800 border border-blue-200"
                  >
                    <AlertDescription>{infoMessage}</AlertDescription>
                  </Alert>
                )}

                <Button
                  type="submit"
                  className="w-full bg-custom-bg hover:bg-custom-bg-600"
                  disabled={isLoading}
                >
                  {isLoading && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  {t("buttons.continue")}
                </Button>
              </motion.form>
            ) : (
              <motion.form
                key="step2"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                onSubmit={handleEmailAuth}
                className="space-y-4"
              >
                <div className="mb-4 p-3 bg-gray-50 rounded-md">
                  <div className="flex flex-col sm:flex-row sm:items-center">
                    <div className="flex-grow mr-2 mb-2 sm:mb-0">
                      <p className="text-sm text-gray-600">{t("email")}:</p>
                      <p className="font-medium text-gray-900 break-all">
                        {formData.email}
                      </p>
                    </div>
                    <button
                      type="button"
                      onClick={() => setStep(1)}
                      className="text-sm text-custom-bg hover:underline self-start sm:self-center flex-shrink-0"
                    >
                      {t("buttons.edit")}
                    </button>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">{t("firstName")}</Label>
                    <Input
                      id="firstName"
                      name="firstName"
                      type="text"
                      placeholder={t("placeholders.firstName")}
                      value={formData.firstName}
                      onChange={handleInputChange}
                      disabled={isLoading}
                      required
                      className="focus-visible:ring-custom-bg focus-visible:border-custom-bg"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">{t("lastName")}</Label>
                    <Input
                      id="lastName"
                      name="lastName"
                      type="text"
                      placeholder={t("placeholders.lastName")}
                      value={formData.lastName}
                      onChange={handleInputChange}
                      disabled={isLoading}
                      required
                      className="focus-visible:ring-custom-bg focus-visible:border-custom-bg"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">{t("password")}</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      placeholder={t("placeholders.password")}
                      value={formData.password}
                      onChange={handleInputChange}
                      disabled={isLoading}
                      required
                      className="focus-visible:ring-custom-bg focus-visible:border-custom-bg pr-10"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                      onClick={togglePasswordVisibility}
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5" />
                      ) : (
                        <Eye className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {infoMessage && (
                  <Alert
                    variant="info"
                    className="bg-blue-50 text-blue-800 border border-blue-200"
                  >
                    <AlertDescription>{infoMessage}</AlertDescription>
                  </Alert>
                )}

                <Button
                  type="submit"
                  className="w-full bg-custom-bg hover:bg-custom-bg-600"
                  disabled={isLoading}
                >
                  {isLoading && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  {t("buttons.emailSignUp")}
                </Button>

                <Button
                  type="button"
                  variant="ghost"
                  className="w-full text-gray-500"
                  onClick={() => setStep(1)}
                  disabled={isLoading}
                >
                  {t("buttons.back")}
                </Button>
              </motion.form>
            )}
          </AnimatePresence>

          <div className="text-center text-sm">
            <span className="text-gray-600">{t("links.haveAccount")}</span>{" "}
            <Link
              href="/auth/signin"
              className="text-custom-bg hover:underline"
            >
              {t("links.signIn")}
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
