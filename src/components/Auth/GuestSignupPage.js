"use client";

import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import {
  Zap,
  Clock,
  Download,
  History,
  Timer,
  X,
  CheckCircle,
  Shield,
  ArrowRight,
  ArrowDown,
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import SignUpForm from "./SignUpForm";
import { ANONYMOUS_USER_LIMITS } from "@/constants/file";

export default function GuestSignupPage() {
  const tCommon = useTranslations("common.guestModeDialog");
  const searchParams = useSearchParams();
  const source = searchParams.get("source") || "back_to_home_dialog";

  // 根据source获取标题文案
  const getTitle = (source) => {
    const titleKey = `guestSignupPage.titles.${source}`;
    try {
      return tCommon(titleKey);
    } catch {
      return tCommon("guestSignupPage.titles.default");
    }
  };

  // 根据source获取描述文案
  const getDescription = (source) => {
    const messageKey = `messages.${source}`;
    try {
      return tCommon(messageKey);
    } catch {
      return tCommon("messages.default");
    }
  };

  return (
    <div className="min-h-screen  flex">
      {/* 左侧区域 */}
      <div className="w-full lg:w-2/3 p-6 bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex flex-col justify-center">
        <div className="max-w-3xl mx-auto">
          {/* 提示文案区域 */}
          <div className="mb-4">
            <div className="flex items-center gap-3">
              <Zap className="w-5 h-5 text-indigo-600" />
              <h1 className="text-xl font-bold text-indigo-700">
                {getTitle(source)}
              </h1>
            </div>
          </div>
          <p className="text-gray-600 mb-4 text-base">
            {getDescription(source)}
          </p>
          {/* 温馨提醒文案 */}
          <div className="flex items-center gap-3 mb-6">
            <CheckCircle className="w-4 h-4 text-indigo-500 flex-shrink-0" />
            <p className="text-base font-semibold text-indigo-700">
              {tCommon("contentWontBeLost")}
            </p>
          </div>

          {/* 卡片区域 */}
          <div className="grid grid-cols-1 md:grid-cols-[1fr_auto_1fr] gap-4">
            {/* Guest Account Card */}
            <Card className="relative bg-gradient-to-br from-gray-50 to-white shadow-md">
              {/* 插画移到最顶部 */}
              <div className="w-20 h-20 mx-auto mt-4 rounded-2xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                <Image
                  src="/guestsignup/guestmode.png"
                  alt="Guest Mode Illustration"
                  width={80}
                  height={80}
                  className="w-full h-full object-cover rounded-2xl"
                />
              </div>
              <CardHeader className="text-center pb-4 pt-4">
                <CardTitle className="text-lg text-gray-700">
                  {tCommon("guestAccess")}
                </CardTitle>
                <div className="flex justify-center mt-2">
                  <Badge
                    variant="secondary"
                    className="bg-gray-400 text-white px-2 py-1 text-xs"
                  >
                    {tCommon("youAreHere")}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <Clock className="w-4 h-4 text-gray-500" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-700">
                        {tCommon("transcription")}
                      </div>
                      <div className="text-sm text-gray-500">
                        {tCommon(
                          "guestSignupPage.guestAccount.transcriptionDesc"
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-gray-700">
                        {ANONYMOUS_USER_LIMITS.TRANSCRIPTION_MINUTES} min
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <Download className="w-4 h-4 text-gray-500" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-700">
                        {tCommon("exports")}
                      </div>
                      <div className="text-sm text-gray-500">
                        {tCommon("guestSignupPage.guestAccount.exportsDesc")}
                      </div>
                    </div>
                    <X className="w-5 h-5 text-red-500" />
                  </div>

                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <History className="w-4 h-4 text-gray-500" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-700">
                        {tCommon("history")}
                      </div>
                      <div className="text-sm text-gray-500">
                        {tCommon("guestSignupPage.guestAccount.historyDesc")}
                      </div>
                    </div>
                    <X className="w-5 h-5 text-red-500" />
                  </div>

                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <Timer className="w-4 h-4 text-gray-500" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-700">
                        {tCommon("usageLimit")}
                      </div>
                      <div className="text-sm text-gray-500">
                        {tCommon("guestSignupPage.guestAccount.usageLimitDesc")}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-gray-700">
                        {tCommon("oneTotal")}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 中间转换/升级图标 */}
            <div className="flex items-center justify-center my-2 md:my-0">
              <div className="w-6 h-6 rounded-full bg-indigo-50 border border-indigo-100 flex items-center justify-center mx-2">
                <ArrowDown className="w-4 h-4 text-indigo-500 md:hidden" />
                <ArrowRight className="w-4 h-4 text-indigo-500 hidden md:block" />
              </div>
            </div>

            {/* Free Account Card */}
            <Card className="relative bg-gradient-to-br from-indigo-50 to-purple-50 shadow-xl">
              {/* 插画移到最顶部 */}
              <div className="w-20 h-20 mx-auto mt-4 bg-gradient-to-br from-indigo-400 to-purple-500 rounded-2xl flex items-center justify-center relative overflow-hidden">
                <Image
                  src="/guestsignup/aftersignup.png"
                  alt="Free Account After Signup Illustration"
                  width={80}
                  height={80}
                  className="w-full h-full object-cover rounded-2xl"
                />
              </div>
              <CardHeader className="text-center pb-4 pt-4">
                <CardTitle className="text-lg text-indigo-700">
                  {tCommon("freeAccount")}
                </CardTitle>
                <div className="flex justify-center mt-2">
                  <Badge className="bg-indigo-500 text-white px-2 py-1 text-xs">
                    {tCommon("afterSignUp")}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-indigo-50 rounded-lg">
                    <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                      <Clock className="w-4 h-4 text-indigo-500" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-700">
                        {tCommon("transcription")}
                      </div>
                      <div className="text-sm text-indigo-600">
                        {tCommon(
                          "guestSignupPage.freeAccount.transcriptionDesc"
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-indigo-600">
                        120 min
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-3 bg-indigo-50 rounded-lg">
                    <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                      <Download className="w-4 h-4 text-indigo-500" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-700">
                        {tCommon("exports")}
                      </div>
                      <div className="text-sm text-indigo-600">
                        {tCommon("guestSignupPage.freeAccount.exportsDesc")}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-indigo-600">
                        {tCommon("sixFormats")}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-3 bg-indigo-50 rounded-lg">
                    <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                      <History className="w-4 h-4 text-indigo-500" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-700">
                        {tCommon("history")}
                      </div>
                      <div className="text-sm text-indigo-600">
                        {tCommon("guestSignupPage.freeAccount.historyDesc")}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-indigo-600">
                        {tCommon("thirtyDays")}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-3 bg-indigo-50 rounded-lg">
                    <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                      <Timer className="w-4 h-4 text-indigo-500" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-700">
                        {tCommon("usageLimit")}
                      </div>
                      <div className="text-sm text-indigo-600">
                        {tCommon("guestSignupPage.freeAccount.usageLimitDesc")}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-indigo-600">
                        {tCommon("threePerDay")}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* 右侧区域 - 注册表单 */}
      <div className="w-full lg:w-1/3 flex items-center justify-center p-16 bg-white">
        <div className="w-full max-w-sm">
          <SignUpForm embedded={true} />
          {/* 信任文案 */}
          <div className="text-center mt-6">
            <div className="flex items-center justify-center gap-4 text-xs text-gray-600 flex-nowrap">
              <div className="flex items-center gap-1">
                <Shield className="w-3 h-3 text-green-600" />
                <span className="whitespace-nowrap">
                  {tCommon("noCreditCardRequired")}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-3 h-3 text-marketing-600" />
                <span className="whitespace-nowrap">
                  {tCommon("signUpTakesThirtySeconds")}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
