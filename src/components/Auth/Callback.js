"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { useAuthStore } from "@/stores/useAuthStore";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Check<PERSON><PERSON>cle2, Loader2, XCircle } from "lucide-react";
import { handleAnonymousFileMigration } from "./AnonymousFileMigration";
import Logo from "@/components/Logo";
import { useRouter } from "@/i18n/navigation";
import { useTranslations } from "next-intl";

const ErrorState = ({ error, onBackToSignIn }) => {
  const t = useTranslations("auth.callback");
  return (
    <div className="flex flex-col items-center space-y-4 w-full">
      <div className="flex items-center justify-center w-12 h-12 rounded-full bg-red-50">
        <XCircle className="h-6 w-6 text-red-600" />
      </div>
      <div className="text-center space-y-1">
        <p className="text-base font-medium text-gray-900">
          {t("error.title")}
        </p>
        <p className="text-sm text-gray-500">{error}</p>
      </div>
      <Button
        variant="outline"
        onClick={onBackToSignIn}
        className="w-full text-white bg-custom-bg hover:bg-custom-bg-600 hover:text-white"
      >
        {t("error.backToSignIn")}
      </Button>
    </div>
  );
};

const SuccessState = ({ message }) => {
  const t = useTranslations("auth.callback");
  return (
    <div className="flex flex-col items-center space-y-4 w-full">
      <div className="flex items-center justify-center w-12 h-12 rounded-full bg-indigo-50">
        <CheckCircle2 className="h-6 w-6 text-indigo-600" />
      </div>
      <div className="text-center space-y-1">
        <p className="text-base font-medium text-gray-900">
          {t("success.title")}
        </p>
        <p className="text-sm text-gray-500">{message}</p>
      </div>
    </div>
  );
};

const LoadingState = () => {
  const t = useTranslations("auth.callback");
  return (
    <div className="flex flex-col items-center space-y-4">
      <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
      <p className="text-base text-gray-600">{t("loading.message")}</p>
    </div>
  );
};

const CallbackHandler = () => {
  const t = useTranslations("auth.callback");
  const router = useRouter();
  const searchParams = useSearchParams();
  const { initialize, session } = useAuthStore();
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");

  useEffect(() => {
    async function handleCallback() {
      try {
        const errorCode = searchParams.get("error_code");
        const errorDescription = searchParams.get("error_description");
        const type = searchParams.get("type");

        if (errorCode) {
          setError(errorDescription || t("error.authFailed"));
          return;
        }

        await initialize({ isCallback: true });
        // 等待一段时间，确保用户信息已加载
        // 这是一个简单的解决方案，更好的方法是实现一个轮询或监听机制
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // 获取最新的用户状态
        const currentUser = useAuthStore.getState().user;

        // 注意：AppSumo 激活逻辑已移至 useAuthStore.js 中的 onAuthStateChange 事件处理
        // 这样可以确保在所有登录方式下都执行相同的激活逻辑

        // 检查是否有订阅意图
        const storedIntent = localStorage.getItem("subscriptionIntent");
        if (storedIntent && !currentUser?.hasActiveSubscription) {
          try {
            const { planId, mode, timestamp } = JSON.parse(storedIntent);

            // 检查数据是否过期
            const isExpired =
              new Date().getTime() - timestamp > 24 * 60 * 60 * 1000;

            if (!isExpired && planId) {
              // 清除存储的意图
              localStorage.removeItem("subscriptionIntent");

              // 创建 checkout session 并重定向
              const { subscriptionService } = await import("@/services");
              const response = await subscriptionService.createCheckoutSession(
                planId,
                null,
                mode,
                "payment_intent_callback"
              );
              const url = response.data.url;

              // 在当前页面重定向到 Stripe
              window.location.href = url;
              return;
            }
          } catch (error) {
            console.error("Error processing subscription intent:", error);
            localStorage.removeItem("subscriptionIntent");
          }
        }

        // 处理文件迁移
        const migrated = await handleAnonymousFileMigration(router, {
          onSuccess: async (fileId) => {
            if (type === "email_verification") {
              setMessage(t("success.redirectingToTranscription"));
              await new Promise((resolve) => setTimeout(resolve, 2000));
            }
            router.push(`/transcriptions/${fileId}`);
          },
        });

        if (!migrated) {
          // 处理没有文件需要迁移的情况
          if (type === "email_verification") {
            setMessage(t("success.redirecting"));
            await new Promise((resolve) => setTimeout(resolve, 2000));
          }
          router.push("/dashboard");
        }
      } catch (error) {
        console.error("Auth callback error:", error);
        setError(t("error.defaultMessage"));
      }
    }

    if (session) {
      router.push("/dashboard");
      return;
    }

    handleCallback();
  }, [router, initialize, session, searchParams, t]);

  const handleBackToSignIn = () => {
    router.push("/auth/signin");
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-sm p-6">
        <CardContent className="flex flex-col items-center space-y-6">
          <div className="relative">
            <Logo />
          </div>
          <h2 className="text-2xl font-semibold text-center">{t("title")}</h2>

          {error ? (
            <ErrorState error={error} onBackToSignIn={handleBackToSignIn} />
          ) : message ? (
            <SuccessState message={message} />
          ) : (
            <LoadingState />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CallbackHandler;
