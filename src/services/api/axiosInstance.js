"use client";

import axios from "axios";
import config from "@/config";
import supabase from "@/lib/supabaseClient";
import * as Sentry from "@sentry/nextjs";

let currentSession = null;

// 初始化时获取 session
const initPromise = supabase.auth.getSession().then(({ data: { session } }) => {
  currentSession = session;
  return session;
});

// 监听 auth 状态变化
const {
  data: { subscription },
} = supabase.auth.onAuthStateChange((event, session) => {
  currentSession = session;
});

const instance = axios.create({
  baseURL: config.API_BASE_URL,
  // 设置默认超时时间为 15 秒
  timeout: 15000,
});

// 创建一个通用的 axios 实例，用于外部请求（如 Cloudflare 上传）
const externalInstance = axios.create({
  // 不设置默认超时，在具体请求中根据文件大小动态设置
});

// 通用重试配置
const RETRY_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000, // 基础延迟 1 秒
  retryDelayMultiplier: 1.5, // 每次重试延迟递增倍数
};

// 判断是否为可重试的错误
const isRetryableError = (error) => {
  console.log("Checking if error is retryable:", {
    message: error.message,
    code: error.code,
    hasResponse: !!error.response,
    status: error.response?.status,
    name: error.name,
  });

  // 用户主动取消的操作不应该重试
  if (error.name === "CanceledError" || error.code === "ERR_CANCELED") {
    console.log("User canceled operation - will not retry");
    return false;
  }

  // 网络错误（包括离线状态）
  if (!error.response) {
    console.log("Network error detected - will retry");
    return true;
  }

  // 超时错误
  if (error.code === "ECONNABORTED") {
    console.log("Timeout error detected - will retry");
    return true;
  }

  // 5xx 服务器错误（除了 501 Not Implemented）
  if (error.response?.status >= 500 && error.response?.status !== 501) {
    console.log("Server error detected - will retry");
    return true;
  }

  // 429 Too Many Requests
  if (error.response?.status === 429) {
    console.log("Rate limit error detected - will retry");
    return true;
  }

  // 408 Request Timeout
  if (error.response?.status === 408) {
    console.log("Request timeout error detected - will retry");
    return true;
  }

  console.log("Error is not retryable");
  return false;
};

// 计算重试延迟时间
const getRetryDelay = (retryCount) => {
  return (
    RETRY_CONFIG.retryDelay *
    Math.pow(RETRY_CONFIG.retryDelayMultiplier, retryCount)
  );
};

// 重试函数
const retryRequest = async (
  originalConfig,
  retryCount = 0,
  axiosInstance = instance
) => {
  if (retryCount >= RETRY_CONFIG.maxRetries) {
    throw new Error(`Max retries (${RETRY_CONFIG.maxRetries}) exceeded`);
  }

  const delay = getRetryDelay(retryCount);
  console.log(
    `Retrying request to ${originalConfig.url} (attempt ${retryCount + 1}/${
      RETRY_CONFIG.maxRetries
    }) after ${delay}ms`
  );

  await new Promise((resolve) => setTimeout(resolve, delay));

  // 创建新的配置对象，避免修改原始配置
  const retryConfig = {
    ...originalConfig,
    __retryCount: retryCount + 1,
  };

  return axiosInstance.request(retryConfig);
};

// 请求拦截器 - 确保 session 已初始化
instance.interceptors.request.use(async (config) => {
  await initPromise;

  if (currentSession?.access_token) {
    config.headers.Authorization = `Bearer ${currentSession.access_token}`;
  }
  return config;
});

// 通用的响应拦截器函数
const createResponseInterceptor = (
  axiosInstance,
  includeAuthHandling = true
) => {
  return axiosInstance.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalConfig = error.config;
      const retryCount = originalConfig?.__retryCount || 0;

      console.log("Response interceptor caught error:", {
        url: originalConfig?.url,
        method: originalConfig?.method,
        retryCount,
        maxRetries: RETRY_CONFIG.maxRetries,
      });

      // 检查是否应该重试（避免无限重试）
      const isRetryable = isRetryableError(error);
      if (isRetryable && retryCount < RETRY_CONFIG.maxRetries) {
        console.log("Starting retry process...");
        try {
          return await retryRequest(originalConfig, retryCount, axiosInstance);
        } catch (retryError) {
          // 如果重试也失败了，继续处理原始错误
          console.error("All retry attempts failed:", retryError);
        }
      } else {
        console.log("Not retrying because:", {
          isRetryable,
          retryCount,
          maxRetries: RETRY_CONFIG.maxRetries,
        });
      }

      // 上报错误到 Sentry（只在最终失败时上报，避免重复上报）
      // 过滤掉 401 和 403 和 404 错误，这些是正常的业务场景（如登录过期、权限不足、资源不存在）
      const shouldReportToSentry =
        error.response?.status !== 401 &&
        error.response?.status !== 403 &&
        error.response?.status !== 404;

      if (shouldReportToSentry) {
        Sentry.captureException(error, {
          extra: {
            url: error.config?.url,
            method: error.config?.method,
            status: error.response?.status,
            responseData: error.response?.data,
            retryCount: retryCount,
            maxRetries: RETRY_CONFIG.maxRetries,
          },
        });
      }

      // 只对主实例处理认证相关错误
      if (includeAuthHandling) {
        // 处理 401 未授权错误
        if (error.response?.status === 401) {
          if (typeof window !== "undefined") {
            window.location.href = "/auth/signin";
          }
          return Promise.reject(error);
        }

        // 处理账号注销错误
        if (error.response?.data?.code === 20003) {
          // 先注销用户
          await supabase.auth.signOut();

          if (typeof window !== "undefined") {
            // 可以考虑添加一个 query 参数来显示特定的错误消息
            window.location.href = "/auth/signin?error=account_deleted";
          }
          return Promise.reject(error);
        }

        // 处理其他 4xx 和 5xx 错误
        if (error.response?.status >= 400 && error.response?.status < 600) {
          const { code, details, message } = error.response.data || {};

          // 构造标准化的错误响应
          const errorResponse = {
            status: error.response.status,
            data: {
              code: code || "UNKNOWN_ERROR",
              details: details || {},
              message: message || "An unexpected error occurred",
            },
          };

          return Promise.reject(errorResponse);
        }
      }

      // 处理其他错误
      return Promise.reject(error);
    }
  );
};

// 为主实例添加响应拦截器（包含认证处理）
createResponseInterceptor(instance, true);

// 为外部实例添加响应拦截器（不包含认证处理）
createResponseInterceptor(externalInstance, false);

// 清理监听器
if (typeof window !== "undefined") {
  window.addEventListener("beforeunload", () => {
    subscription.unsubscribe();
  });
}

export default instance;
export { externalInstance };
